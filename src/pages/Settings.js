import React, { useState, useEffect } from 'react';
import apiService from '../services/api.service';
import { ENDPOINTS } from '../config/api.config';
import { FiSave, FiRotateCw, FiAlertCircle, FiCheckCircle, FiPlus, FiTrash2, FiMoon, FiSun, FiUser, FiSettings, FiBell, FiLink, FiFilter } from 'react-icons/fi';
import { useTheme } from '../context/ThemeContext';
import { translations } from '../config/translations';
import '../styles/Settings.css';

const Settings = () => {
  const { theme, language, toggleTheme, changeLanguage } = useTheme();
  const t = translations[language].settings;

  const [settings, setSettings] = useState({
    // UI Settings
    itemsPerPage: 10,

    // Notification Settings
    emailNotifications: true,
    userCreationNotify: true,
    userModificationNotify: true,

    // Connection Settings
    connections: [
      { id: 1, name: 'DataQuest', type: 'platform', url: 'https://dataquest.example.com' },
      { id: 2, name: 'WLP', type: 'platform', url: 'https://wlp.example.com' },
      { id: 3, name: 'ADS', type: 'platform', url: 'https://ads.example.com' },
      { id: 4, name: 'SBV', type: 'platform', url: 'https://sbv.example.com' },
      { id: 5, name: 'Ecommerce', type: 'platform', url: 'https://ecommerce.example.com' }
    ],

    // Approver & Executor Settings
    approvers: {
      'access-platform': {
        whitelist: { approvers: ['user1', 'user2'], executors: ['admin1'] },
        funnel: { approvers: ['user3'], executors: ['admin2'] },
        zdslab: { approvers: ['user1', 'user4'], executors: ['admin1'] },
        'ads-report': { approvers: ['user2', 'user5'], executors: ['admin3'] },
        'sbv-report': { approvers: ['user3', 'user6'], executors: ['admin2'] },
        'campaign-config': { approvers: ['user1', 'user7'], executors: ['admin1'] }
      },
      'adhoc-data': { approvers: ['user8', 'user9'], executors: ['admin4'] },
      'access-database': { approvers: ['user10', 'user11'], executors: ['admin5'] },
      'access-report': { approvers: ['user12', 'user13'], executors: ['admin6'] }
    },

    // Filter Settings
    filters: {
      dataquest: {
        status: ['active', 'inactive', 'pending'],
        roles: ['admin', 'user', 'viewer'],
        departments: ['IT', 'Sales', 'Marketing']
      },
      wlp: {
        status: ['active', 'inactive'],
        roles: ['admin', 'user'],
        departments: ['Finance', 'Operations']
      },
      ads: {
        status: ['active', 'inactive', 'suspended'],
        roles: ['admin', 'user', 'editor'],
        departments: ['Content', 'Design']
      },
      sbv: {
        status: ['active', 'inactive'],
        roles: ['admin', 'user'],
        departments: ['Banking', 'Customer Service']
      },
      ecommerce: {
        status: ['active', 'inactive', 'trial'],
        roles: ['admin', 'user', 'manager'],
        departments: ['Sales', 'Support', 'Warehouse']
      }
    }
  });

  const [newConnection, setNewConnection] = useState({ name: '', type: 'platform', url: '' });
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [activeTab, setActiveTab] = useState('ui');

  // New state for adding approvers, reviewers, and executors
  // Multi-input state for each platform/requestType
  const [newApprover, setNewApprover] = useState({});
  const [newReviewer, setNewReviewer] = useState({});
  const [newExecutor, setNewExecutor] = useState({});

  // State for user lists
  const [approversList, setApproversList] = useState([]);
  const [reviewersList, setReviewersList] = useState([]);
  const [executorsList, setExecutorsList] = useState([]);
  const [filteredApprovers, setFilteredApprovers] = useState({});
  const [filteredReviewers, setFilteredReviewers] = useState({});
  const [filteredExecutors, setFilteredExecutors] = useState({});
  const [showSuggestions, setShowSuggestions] = useState({
    approvers: {},
    reviewers: {},
    executors: {}
  });

  // Fetch user lists and approval config from API on mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch approval config
        const configResponse = await apiService.get(ENDPOINTS.CONFIG.GET);
        // Transform array to nested object
        const grouped = {};
        // Store the original config items for reference (needed for delete operations)
        const configItems = {};

        (configResponse || []).forEach(item => {
          const { id, request_type, platform, role, display_name } = item;
          if (!grouped[request_type]) grouped[request_type] = {};
          if (!grouped[request_type][platform]) grouped[request_type][platform] = {
            approvers: [],
            reviewers: [],
            executors: [],
            approverConfigs: {}, // Map of display_name -> config_id
            reviewerConfigs: {}, // Map of display_name -> config_id
            executorConfigs: {}  // Map of display_name -> config_id
          };

          if (role === 'approver') {
            grouped[request_type][platform].approvers.push(display_name);
            grouped[request_type][platform].approverConfigs[display_name] = id;
          }
          if (role === 'reviewer') {
            if (!grouped[request_type][platform].reviewers) {
              grouped[request_type][platform].reviewers = [];
              grouped[request_type][platform].reviewerConfigs = {};
            }
            grouped[request_type][platform].reviewers.push(display_name);
            grouped[request_type][platform].reviewerConfigs[display_name] = id;
          }
          if (role === 'executor') {
            grouped[request_type][platform].executors.push(display_name);
            grouped[request_type][platform].executorConfigs[display_name] = id;
          }

          // Store the full config item for reference
          configItems[id] = item;
        });

        setSettings(prev => ({
          ...prev,
          approvers: grouped,
          configItems: configItems
        }));

        // Fetch approvers list
        const approversResponse = await apiService.get(ENDPOINTS.USERS.GET_BASIC('approver'));
        setApproversList(approversResponse || []);

        // Fetch reviewers list
        const reviewersResponse = await apiService.get(ENDPOINTS.USERS.GET_BASIC('reviewer'));
        setReviewersList(reviewersResponse || []);

        // Fetch executors list
        const executorsResponse = await apiService.get(ENDPOINTS.USERS.GET_BASIC('executor'));
        setExecutorsList(executorsResponse || []);

      } catch (err) {
        console.error('Failed to fetch data:', err);
        setErrorMessage(language === 'en' ? 'Error loading data. Please try again.' : 'Lỗi khi tải dữ liệu. Vui lòng thử lại.');
        setTimeout(() => setErrorMessage(''), 3000);
      }
    };

    fetchData();
  }, [language]);

  // Handlers for Approvers
  const handleNewApproverInput = (requestType, platform, value) => {
    setNewApprover(prev => ({ ...prev, [`${requestType}_${platform}`]: value }));

    // Filter approvers list based on input value
    if (value.trim() === '') {
      // Clear filtered list when input is empty
      setFilteredApprovers(prev => ({ ...prev, [`${requestType}_${platform}`]: [] }));
      setShowSuggestions(prev => ({
        ...prev,
        approvers: { ...prev.approvers, [`${requestType}_${platform}`]: false }
      }));
    } else {
      const filtered = approversList.filter(user =>
        user.display_name.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredApprovers(prev => ({ ...prev, [`${requestType}_${platform}`]: filtered }));
      setShowSuggestions(prev => ({
        ...prev,
        approvers: { ...prev.approvers, [`${requestType}_${platform}`]: true }
      }));
    }
  };

  // Handle selecting an approver from suggestions
  const handleSelectApprover = (requestType, platform, user) => {
    // Store both display_name (for UI) and id (for API) in the state
    setNewApprover(prev => ({
      ...prev,
      [`${requestType}_${platform}`]: user.display_name,
      [`${requestType}_${platform}_id`]: user.id // Store the user ID for API calls
    }));

    setShowSuggestions(prev => ({
      ...prev,
      approvers: { ...prev.approvers, [`${requestType}_${platform}`]: false }
    }));
  };

  // Handlers for Reviewers
  const handleNewReviewerInput = (requestType, platform, value) => {
    setNewReviewer(prev => ({ ...prev, [`${requestType}_${platform}`]: value }));

    // Filter reviewers list based on input value
    if (value.trim() === '') {
      // Clear filtered list when input is empty
      setFilteredReviewers(prev => ({ ...prev, [`${requestType}_${platform}`]: [] }));
      setShowSuggestions(prev => ({
        ...prev,
        reviewers: { ...prev.reviewers, [`${requestType}_${platform}`]: false }
      }));
    } else {
      const filtered = reviewersList.filter(user =>
        user.display_name.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredReviewers(prev => ({ ...prev, [`${requestType}_${platform}`]: filtered }));
      setShowSuggestions(prev => ({
        ...prev,
        reviewers: { ...prev.reviewers, [`${requestType}_${platform}`]: true }
      }));
    }
  };

  // Handle selecting a reviewer from suggestions
  const handleSelectReviewer = (requestType, platform, user) => {
    // Store both display_name (for UI) and id (for API) in the state
    setNewReviewer(prev => ({
      ...prev,
      [`${requestType}_${platform}`]: user.display_name,
      [`${requestType}_${platform}_id`]: user.id // Store the user ID for API calls
    }));

    setShowSuggestions(prev => ({
      ...prev,
      reviewers: { ...prev.reviewers, [`${requestType}_${platform}`]: false }
    }));
  };

  const handleAddApprover = async (requestType, platform) => {
    const key = `${requestType}_${platform}`;
    const displayName = (newApprover[key] || '').trim();
    const userId = newApprover[`${key}_id`];

    if (!displayName) return;

    // Hide suggestions
    setShowSuggestions(prev => ({
      ...prev,
      approvers: { ...prev.approvers, [`${requestType}_${platform}`]: false }
    }));

    try {
      // If we don't have a user ID (manual entry), try to find it in the approvers list
      let userIdToUse = userId;

      if (!userIdToUse) {
        // Try to find the user in the approvers list by display_name
        const foundUser = approversList.find(u => u.display_name === displayName);
        if (foundUser) {
          userIdToUse = foundUser.id;
        } else {
          // If we can't find the user, show an error
          setErrorMessage(language === 'en' ? 'Please select a valid approver from the suggestions.' : 'Vui lòng chọn người duyệt hợp lệ từ gợi ý.');
          setTimeout(() => setErrorMessage(''), 3000);
          return;
        }
      }

      // First, add to API
      const configData = {
        request_type: requestType,
        platform: platform,
        role: 'approver',
        user_id: userIdToUse,
        display_name: displayName
      };

      // Call the API to create the config
      const response = await apiService.post(ENDPOINTS.CONFIG.CREATE, configData);

      // If successful, update the local state
      if (response && response.id) {
        setSettings(prev => {
          const updated = { ...prev };
          if (!updated.approvers[requestType][platform].approvers.includes(displayName)) {
            updated.approvers = { ...updated.approvers };
            updated.approvers[requestType] = { ...updated.approvers[requestType] };
            updated.approvers[requestType][platform] = { ...updated.approvers[requestType][platform] };
            updated.approvers[requestType][platform].approvers = [...updated.approvers[requestType][platform].approvers, displayName];
            // Store the config id for this user
            updated.approvers[requestType][platform].approverConfigs[displayName] = response.id;
            // Also store in the configItems
            updated.configItems = { ...updated.configItems, [response.id]: configData };
          }
          return updated;
        });

        // Show success message
        setSuccessMessage(language === 'en' ? 'Approver added successfully!' : 'Đã thêm người duyệt thành công!');
        setTimeout(() => setSuccessMessage(''), 3000);
      }
    } catch (error) {
      console.error('Error adding approver:', error);
      setErrorMessage(language === 'en' ? 'Error adding approver. Please try again.' : 'Lỗi khi thêm người duyệt. Vui lòng thử lại.');
      setTimeout(() => setErrorMessage(''), 3000);
      return;
    }

    // Clear the input field and stored ID
    setNewApprover(prev => ({
      ...prev,
      [key]: '',
      [`${key}_id`]: undefined
    }));
  };
  const handleRemoveApprover = async (requestType, platform, user) => {
    try {
      // Get the config_id for this user
      const configId = settings.approvers[requestType][platform].approverConfigs[user];

      if (!configId) {
        console.error('Config ID not found for approver:', user);
        setErrorMessage(language === 'en' ? 'Error removing approver. Config not found.' : 'Lỗi khi xóa người duyệt. Không tìm thấy cấu hình.');
        setTimeout(() => setErrorMessage(''), 3000);
        return;
      }

      // Call the API to delete the config
      await apiService.post(ENDPOINTS.CONFIG.DELETE(configId));

      // Update the local state
      setSettings(prev => {
        const updated = { ...prev };
        updated.approvers = { ...updated.approvers };
        updated.approvers[requestType] = { ...updated.approvers[requestType] };
        updated.approvers[requestType][platform] = { ...updated.approvers[requestType][platform] };
        updated.approvers[requestType][platform].approvers = updated.approvers[requestType][platform].approvers.filter(u => u !== user);

        // Remove from the config maps
        const { [user]: removed, ...restApproverConfigs } = updated.approvers[requestType][platform].approverConfigs;
        updated.approvers[requestType][platform].approverConfigs = restApproverConfigs;

        // Remove from configItems
        const { [configId]: removedConfig, ...restConfigItems } = updated.configItems;
        updated.configItems = restConfigItems;

        return updated;
      });

      // Show success message
      setSuccessMessage(language === 'en' ? 'Approver removed successfully!' : 'Đã xóa người duyệt thành công!');
      setTimeout(() => setSuccessMessage(''), 3000);

    } catch (error) {
      console.error('Error removing approver:', error);
      setErrorMessage(language === 'en' ? 'Error removing approver. Please try again.' : 'Lỗi khi xóa người duyệt. Vui lòng thử lại.');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  const handleAddReviewer = async (requestType, platform) => {
    const key = `${requestType}_${platform}`;
    const displayName = (newReviewer[key] || '').trim();
    const userId = newReviewer[`${key}_id`];

    if (!displayName) return;

    // Hide suggestions
    setShowSuggestions(prev => ({
      ...prev,
      reviewers: { ...prev.reviewers, [`${requestType}_${platform}`]: false }
    }));

    try {
      // If we don't have a user ID (manual entry), try to find it in the reviewers list
      let userIdToUse = userId;

      if (!userIdToUse) {
        // Try to find the user in the reviewers list by display_name
        const foundUser = reviewersList.find(u => u.display_name === displayName);
        if (foundUser) {
          userIdToUse = foundUser.id;
        } else {
          // If we can't find the user, show an error
          setErrorMessage(language === 'en' ? 'Please select a valid reviewer from the suggestions.' : 'Vui lòng chọn người xem xét hợp lệ từ gợi ý.');
          setTimeout(() => setErrorMessage(''), 3000);
          return;
        }
      }

      // First, add to API
      const configData = {
        request_type: requestType,
        platform: platform,
        role: 'reviewer',
        user_id: userIdToUse,
        display_name: displayName
      };

      // Call the API to create the config
      const response = await apiService.post(ENDPOINTS.CONFIG.CREATE, configData);

      // If successful, update the local state
      if (response && response.id) {
        setSettings(prev => {
          const updated = { ...prev };
          if (!updated.approvers[requestType][platform].reviewers.includes(displayName)) {
            updated.approvers = { ...updated.approvers };
            updated.approvers[requestType] = { ...updated.approvers[requestType] };
            updated.approvers[requestType][platform] = { ...updated.approvers[requestType][platform] };
            updated.approvers[requestType][platform].reviewers = [...updated.approvers[requestType][platform].reviewers, displayName];
            // Store the config id for this user
            updated.approvers[requestType][platform].reviewerConfigs[displayName] = response.id;
            // Also store in the configItems
            updated.configItems = { ...updated.configItems, [response.id]: configData };
          }
          return updated;
        });

        // Show success message
        setSuccessMessage(language === 'en' ? 'Reviewer added successfully!' : 'Đã thêm người xem xét thành công!');
        setTimeout(() => setSuccessMessage(''), 3000);
      }
    } catch (error) {
      console.error('Error adding reviewer:', error);
      setErrorMessage(language === 'en' ? 'Error adding reviewer. Please try again.' : 'Lỗi khi thêm người xem xét. Vui lòng thử lại.');
      setTimeout(() => setErrorMessage(''), 3000);
      return;
    }

    // Clear the input field and stored ID
    setNewReviewer(prev => ({
      ...prev,
      [key]: '',
      [`${key}_id`]: undefined
    }));
  };

  const handleRemoveReviewer = async (requestType, platform, user) => {
    try {
      // Get the config_id for this user
      const configId = settings.approvers[requestType][platform].reviewerConfigs[user];

      if (!configId) {
        console.error('Config ID not found for reviewer:', user);
        setErrorMessage(language === 'en' ? 'Error removing reviewer. Config not found.' : 'Lỗi khi xóa người xem xét. Không tìm thấy cấu hình.');
        setTimeout(() => setErrorMessage(''), 3000);
        return;
      }

      // Call the API to delete the config
      await apiService.post(ENDPOINTS.CONFIG.DELETE(configId));

      // Update the local state
      setSettings(prev => {
        const updated = { ...prev };
        updated.approvers = { ...updated.approvers };
        updated.approvers[requestType] = { ...updated.approvers[requestType] };
        updated.approvers[requestType][platform] = { ...updated.approvers[requestType][platform] };
        updated.approvers[requestType][platform].reviewers = updated.approvers[requestType][platform].reviewers.filter(u => u !== user);

        // Remove from the config maps
        const { [user]: removed, ...restReviewerConfigs } = updated.approvers[requestType][platform].reviewerConfigs;
        updated.approvers[requestType][platform].reviewerConfigs = restReviewerConfigs;

        // Remove from configItems
        const { [configId]: removedConfig, ...restConfigItems } = updated.configItems;
        updated.configItems = restConfigItems;

        return updated;
      });

      // Show success message
      setSuccessMessage(language === 'en' ? 'Reviewer removed successfully!' : 'Đã xóa người xem xét thành công!');
      setTimeout(() => setSuccessMessage(''), 3000);

    } catch (error) {
      console.error('Error removing reviewer:', error);
      setErrorMessage(language === 'en' ? 'Error removing reviewer. Please try again.' : 'Lỗi khi xóa người xem xét. Vui lòng thử lại.');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // Handlers for Executors
  const handleNewExecutorInput = (requestType, platform, value) => {
    setNewExecutor(prev => ({ ...prev, [`${requestType}_${platform}`]: value }));

    // Filter executors list based on input value
    if (value.trim() === '') {
      // Clear filtered list when input is empty
      setFilteredExecutors(prev => ({ ...prev, [`${requestType}_${platform}`]: [] }));
      setShowSuggestions(prev => ({
        ...prev,
        executors: { ...prev.executors, [`${requestType}_${platform}`]: false }
      }));
    } else {
      const filtered = executorsList.filter(user =>
        user.display_name.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredExecutors(prev => ({ ...prev, [`${requestType}_${platform}`]: filtered }));
      setShowSuggestions(prev => ({
        ...prev,
        executors: { ...prev.executors, [`${requestType}_${platform}`]: true }
      }));
    }
  };

  // Handle selecting an executor from suggestions
  const handleSelectExecutor = (requestType, platform, user) => {
    // Store both display_name (for UI) and id (for API) in the state
    setNewExecutor(prev => ({
      ...prev,
      [`${requestType}_${platform}`]: user.display_name,
      [`${requestType}_${platform}_id`]: user.id // Store the user ID for API calls
    }));

    setShowSuggestions(prev => ({
      ...prev,
      executors: { ...prev.executors, [`${requestType}_${platform}`]: false }
    }));
  };

  const handleAddExecutor = async (requestType, platform) => {
    const key = `${requestType}_${platform}`;
    const displayName = (newExecutor[key] || '').trim();
    const userId = newExecutor[`${key}_id`];

    if (!displayName) return;

    // Hide suggestions
    setShowSuggestions(prev => ({
      ...prev,
      executors: { ...prev.executors, [`${requestType}_${platform}`]: false }
    }));

    try {
      // If we don't have a user ID (manual entry), try to find it in the executors list
      let userIdToUse = userId;

      if (!userIdToUse) {
        // Try to find the user in the executors list by display_name
        const foundUser = executorsList.find(u => u.display_name === displayName);
        if (foundUser) {
          userIdToUse = foundUser.id;
        } else {
          // If we can't find the user, show an error
          setErrorMessage(language === 'en' ? 'Please select a valid executor from the suggestions.' : 'Vui lòng chọn người thực thi hợp lệ từ gợi ý.');
          setTimeout(() => setErrorMessage(''), 3000);
          return;
        }
      }

      // First, add to API
      const configData = {
        request_type: requestType,
        platform: platform,
        role: 'executor',
        user_id: userIdToUse,
        display_name: displayName
      };

      // Call the API to create the config
      const response = await apiService.post(ENDPOINTS.CONFIG.CREATE, configData);

      // If successful, update the local state
      if (response && response.id) {
        setSettings(prev => {
          const updated = { ...prev };
          if (!updated.approvers[requestType][platform].executors.includes(displayName)) {
            updated.approvers = { ...updated.approvers };
            updated.approvers[requestType] = { ...updated.approvers[requestType] };
            updated.approvers[requestType][platform] = { ...updated.approvers[requestType][platform] };
            updated.approvers[requestType][platform].executors = [...updated.approvers[requestType][platform].executors, displayName];
            // Store the config id for this user
            updated.approvers[requestType][platform].executorConfigs[displayName] = response.id;
            // Also store in the configItems
            updated.configItems = { ...updated.configItems, [response.id]: configData };
          }
          return updated;
        });

        // Show success message
        setSuccessMessage(language === 'en' ? 'Executor added successfully!' : 'Đã thêm người thực thi thành công!');
        setTimeout(() => setSuccessMessage(''), 3000);
      }
    } catch (error) {
      console.error('Error adding executor:', error);
      setErrorMessage(language === 'en' ? 'Error adding executor. Please try again.' : 'Lỗi khi thêm người thực thi. Vui lòng thử lại.');
      setTimeout(() => setErrorMessage(''), 3000);
      return;
    }

    // Clear the input field and stored ID
    setNewExecutor(prev => ({
      ...prev,
      [key]: '',
      [`${key}_id`]: undefined
    }));
  };
  const handleRemoveExecutor = async (requestType, platform, user) => {
    try {
      // Get the config_id for this user
      const configId = settings.approvers[requestType][platform].executorConfigs[user];

      if (!configId) {
        console.error('Config ID not found for executor:', user);
        setErrorMessage(language === 'en' ? 'Error removing executor. Config not found.' : 'Lỗi khi xóa người thực thi. Không tìm thấy cấu hình.');
        setTimeout(() => setErrorMessage(''), 3000);
        return;
      }

      // Call the API to delete the config
      await apiService.post(ENDPOINTS.CONFIG.DELETE(configId));

      // Update the local state
      setSettings(prev => {
        const updated = { ...prev };
        updated.approvers = { ...updated.approvers };
        updated.approvers[requestType] = { ...updated.approvers[requestType] };
        updated.approvers[requestType][platform] = { ...updated.approvers[requestType][platform] };
        updated.approvers[requestType][platform].executors = updated.approvers[requestType][platform].executors.filter(u => u !== user);

        // Remove from the config maps
        const { [user]: removed, ...restExecutorConfigs } = updated.approvers[requestType][platform].executorConfigs;
        updated.approvers[requestType][platform].executorConfigs = restExecutorConfigs;

        // Remove from configItems
        const { [configId]: removedConfig, ...restConfigItems } = updated.configItems;
        updated.configItems = restConfigItems;

        return updated;
      });

      // Show success message
      setSuccessMessage(language === 'en' ? 'Executor removed successfully!' : 'Đã xóa người thực thi thành công!');
      setTimeout(() => setSuccessMessage(''), 3000);

    } catch (error) {
      console.error('Error removing executor:', error);
      setErrorMessage(language === 'en' ? 'Error removing executor. Please try again.' : 'Lỗi khi xóa người thực thi. Vui lòng thử lại.');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // Handle tab change
  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSettings({
      ...settings,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleNumberChange = (e) => {
    const { name, value } = e.target;
    setSettings({
      ...settings,
      [name]: parseInt(value, 10) || 0
    });
  };

  const handleNewConnectionChange = (e) => {
    const { name, value } = e.target;
    setNewConnection({
      ...newConnection,
      [name]: value
    });
  };

  const addConnection = () => {
    if (!newConnection.name || !newConnection.url) {
      setErrorMessage(language === 'en' ? 'Please fill in all connection details' : 'Vui lòng điền đầy đủ thông tin kết nối');
      setTimeout(() => setErrorMessage(''), 3000);
      return;
    }

    const newId = Math.max(...settings.connections.map(c => c.id), 0) + 1;
    const updatedConnections = [...settings.connections, { ...newConnection, id: newId }];

    setSettings({
      ...settings,
      connections: updatedConnections,
      filters: {
        ...settings.filters,
        [newConnection.name.toLowerCase()]: {
          status: ['active', 'inactive'],
          roles: ['admin', 'user'],
          departments: ['Default']
        }
      }
    });

    setNewConnection({ name: '', type: 'platform', url: '' });
    setSuccessMessage(language === 'en' ? 'Connection added successfully' : 'Đã thêm kết nối thành công');
    setTimeout(() => setSuccessMessage(''), 3000);
  };

  const removeConnection = (id) => {
    const connection = settings.connections.find(c => c.id === id);
    const updatedConnections = settings.connections.filter(c => c.id !== id);

    setSettings({
      ...settings,
      connections: updatedConnections,
      filters: Object.fromEntries(
        Object.entries(settings.filters).filter(([key]) => key !== connection.name.toLowerCase())
      )
    });

    setSuccessMessage(language === 'en' ? 'Connection removed successfully' : 'Đã xóa kết nối thành công');
    setTimeout(() => setSuccessMessage(''), 3000);
  };

  const addFilterOption = (platform, filterType, value) => {
    if (!value) return;

    setSettings({
      ...settings,
      filters: {
        ...settings.filters,
        [platform]: {
          ...settings.filters[platform],
          [filterType]: [...settings.filters[platform][filterType], value]
        }
      }
    });
  };

  const removeFilterOption = (platform, filterType, value) => {
    setSettings({
      ...settings,
      filters: {
        ...settings.filters,
        [platform]: {
          ...settings.filters[platform],
          [filterType]: settings.filters[platform][filterType].filter(v => v !== value)
        }
      }
    });
  };

  const saveSettings = () => {
    try {
      // console.log('Saving settings:', settings);
      localStorage.setItem('appSettings', JSON.stringify(settings));

      setSuccessMessage(language === 'en' ? 'Settings saved successfully!' : 'Đã lưu cài đặt thành công!');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      setErrorMessage(language === 'en' ? 'Error saving settings. Please try again.' : 'Lỗi khi lưu cài đặt. Vui lòng thử lại.');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  const resetSettings = () => {
    setSettings({
      itemsPerPage: 10,
      emailNotifications: true,
      userCreationNotify: true,
      userModificationNotify: true,
      connections: [
        { id: 1, name: 'DataQuest', type: 'platform', url: 'https://dataquest.example.com' },
        { id: 2, name: 'WLP', type: 'platform', url: 'https://wlp.example.com' },
        { id: 3, name: 'ADS', type: 'platform', url: 'https://ads.example.com' },
        { id: 4, name: 'SBV', type: 'platform', url: 'https://sbv.example.com' },
        { id: 5, name: 'Ecommerce', type: 'platform', url: 'https://ecommerce.example.com' }
      ],
      filters: {
        dataquest: {
          status: ['active', 'inactive', 'pending'],
          roles: ['admin', 'user', 'viewer'],
          departments: ['IT', 'Sales', 'Marketing']
        },
        wlp: {
          status: ['active', 'inactive'],
          roles: ['admin', 'user'],
          departments: ['Finance', 'Operations']
        },
        ads: {
          status: ['active', 'inactive', 'suspended'],
          roles: ['admin', 'user', 'editor'],
          departments: ['Content', 'Design']
        },
        sbv: {
          status: ['active', 'inactive'],
          roles: ['admin', 'user'],
          departments: ['Banking', 'Customer Service']
        },
        ecommerce: {
          status: ['active', 'inactive', 'trial'],
          roles: ['admin', 'user', 'manager'],
          departments: ['Sales', 'Support', 'Warehouse']
        }
      }
    });

    setSuccessMessage(language === 'en' ? 'Settings reset to default!' : 'Đã khôi phục cài đặt về mặc định!');
    setTimeout(() => setSuccessMessage(''), 3000);
  };

  return (
    <div className="content-container">
      <div className="settings-header">
        <h2>{t.title}</h2>
        <div className="settings-actions">
          <button className="btn-reset" onClick={resetSettings}>
            <FiRotateCw /> {t.actions.reset}
          </button>
          <button className="btn-save" onClick={saveSettings}>
            <FiSave /> {t.actions.save}
          </button>
        </div>
      </div>

      {successMessage && (
        <div className="success-message">
          <FiCheckCircle />
          <span>{successMessage}</span>
        </div>
      )}

      {errorMessage && (
        <div className="error-message">
          <FiAlertCircle />
          <span>{errorMessage}</span>
        </div>
      )}

      <div className="settings-tabs-container">
        <div className="settings-tabs-header">
          <button
            className={`tab-button ${activeTab === 'ui' ? 'active' : ''}`}
            onClick={() => handleTabChange('ui')}
          >
            <FiSettings className="setting-tab-icon" />
            <span>{t.ui.title}</span>
          </button>
          <button
            className={`tab-button ${activeTab === 'notifications' ? 'active' : ''}`}
            onClick={() => handleTabChange('notifications')}
          >
            <FiBell className="setting-tab-icon" />
            <span>{t.notifications.title}</span>
          </button>
          <button
            className={`tab-button ${activeTab === 'connections' ? 'active' : ''}`}
            onClick={() => handleTabChange('connections')}
          >
            <FiLink className="setting-tab-icon" />
            <span>{t.connections.title}</span>
          </button>
          <button
            className={`tab-button ${activeTab === 'approvers' ? 'active' : ''}`}
            onClick={() => handleTabChange('approvers')}
          >
            <FiUser className="setting-tab-icon" />
            <span>{t.approvers.title}</span>
          </button>
          <button
            className={`tab-button ${activeTab === 'filters' ? 'active' : ''}`}
            onClick={() => handleTabChange('filters')}
          >
            <FiFilter className="setting-tab-icon" />
            <span>{t.filters?.title || 'Filters'}</span>
          </button>
        </div>

        <div className="settings-tab-content">
          {/* UI Settings Tab */}
          {activeTab === 'ui' && (
            <div className="setting-tab-panel">
              <div className="settings-grid">
                <div className="form-group">
                  <label>{t.ui.theme}</label>
                  <div className="setting-theme-toggle">
                    <button
                      className={`setting-theme-btn ${theme === 'light' ? 'active' : ''}`}
                      onClick={() => toggleTheme()}
                    >
                      <FiSun /> {t.ui.light}
                    </button>
                    <button
                      className={`setting-theme-btn ${theme === 'dark' ? 'active' : ''}`}
                      onClick={() => toggleTheme()}
                    >
                      <FiMoon /> {t.ui.dark}
                    </button>
                  </div>
                </div>

                <div className="form-group">
                  <label>{t.ui.language}</label>
                  <select
                    value={language}
                    onChange={(e) => changeLanguage(e.target.value)}
                  >
                    <option value="en">English</option>
                    <option value="vi">Tiếng Việt</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>{t.ui.itemsPerPage}</label>
                  <select
                    name="itemsPerPage"
                    value={settings.itemsPerPage}
                    onChange={handleNumberChange}
                  >
                    <option value="5">5</option>
                    <option value="10">10</option>
                    <option value="20">20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {/* Notifications Settings Tab */}
          {activeTab === 'notifications' && (
            <div className="setting-tab-panel">
              <div className="settings-grid">
                <div className="form-group toggle-group">
                  <label>{t.notifications.email}</label>
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      name="emailNotifications"
                      checked={settings.emailNotifications}
                      onChange={handleChange}
                    />
                    <span className="toggle-slider"></span>
                  </label>
                </div>

                <div className="form-group toggle-group">
                  <label>{t.notifications.userCreation}</label>
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      name="userCreationNotify"
                      checked={settings.userCreationNotify}
                      onChange={handleChange}
                      disabled={!settings.emailNotifications}
                    />
                    <span className="toggle-slider"></span>
                  </label>
                </div>

                <div className="form-group toggle-group">
                  <label>{t.notifications.userModification}</label>
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      name="userModificationNotify"
                      checked={settings.userModificationNotify}
                      onChange={handleChange}
                      disabled={!settings.emailNotifications}
                    />
                    <span className="toggle-slider"></span>
                  </label>
                </div>
              </div>
            </div>
          )}

          {/* Connections Settings Tab */}
          {activeTab === 'connections' && (
            <div className="setting-tab-panel">
              <div className="connections-list">
                {(settings.connections || []).map(connection => (
                  <div key={connection.id} className="connection-item">
                    <div className="connection-info">
                      <span className="connection-name">{connection.name}</span>
                      <span className="connection-type">{connection.type}</span>
                      <span className="connection-url">{connection.url}</span>
                    </div>
                    <button
                      className="btn-delete"
                      onClick={() => removeConnection(connection.id)}
                    >
                      <FiTrash2 />
                    </button>
                  </div>
                ))}
              </div>

              <div className="add-connection">
                <h4>{t.connections.addNew}</h4>
                <div className="connection-form">
                  <input
                    type="text"
                    name="name"
                    placeholder={t.connections.name}
                    value={newConnection.name}
                    onChange={handleNewConnectionChange}
                  />
                  <select
                    name="type"
                    value={newConnection.type}
                    onChange={handleNewConnectionChange}
                  >
                    <option value="platform">Platform</option>
                    <option value="datasource">Datasource</option>
                  </select>
                  <input
                    type="text"
                    name="url"
                    placeholder={t.connections.url}
                    value={newConnection.url}
                    onChange={handleNewConnectionChange}
                  />
                  <button className="btn-add" onClick={addConnection}>
                    <FiPlus /> {t.connections.add}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Approvers Settings Tab */}
          {activeTab === 'approvers' && (
            <div className="setting-tab-panel">
              <div className="setting-approvers-container">
                {Object.entries(settings.approvers || {}).map(([requestType, platforms]) => (
                  <div key={requestType} className="setting-request-type-section">
                    {/* Use translation for request type title if available */}
                    <h4>{t.approvers.requestTypes?.[requestType] || requestType.replace('-', ' ').replace(/(?:^|\s)\S/g, a => a.toUpperCase())}</h4>
                    {Object.entries(platforms || {}).map(([platform, config]) => (
                      <div key={platform} className="setting-platform-config">
                        <h5>{platform.replace('-', ' ').replace(/(?:^|\s)\S/g, a => a.toUpperCase())}</h5>
                        <div className="config-group">
                          <div className="config-item">
                            <label>{t.approvers.approvers}</label>
                            <div className="setting-user-list">
                              {(config.approvers || []).map(user => (
                                <div key={user} className="setting-user-item">
                                  <span>{user}</span>
                                  <button className="btn-remove-option" onClick={() => handleRemoveApprover(requestType, platform, user)} title="Remove approver">×</button>
                                </div>
                              ))}
                            </div>
                          <div style={{ display: 'flex', flexDirection: 'column', marginTop: 6, position: 'relative' }}>
                            <div style={{ display: 'flex' }}>
                              <input
                                type="text"
                                className="setting-user-input"
                                placeholder={t.approvers.addApprover || "Add approver"}
                                value={newApprover[`${requestType}_${platform}`] || ''}
                                onChange={e => handleNewApproverInput(requestType, platform, e.target.value)}
                                onKeyDown={e => {
                                  if (e.key === 'Enter') {
                                    handleAddApprover(requestType, platform);
                                  } else if (e.key === 'Escape') {
                                    setShowSuggestions(prev => ({
                                      ...prev,
                                      approvers: { ...prev.approvers, [`${requestType}_${platform}`]: false }
                                    }));
                                  }
                                }}
                                onBlur={() => {
                                  // Delay hiding suggestions to allow for clicks
                                  setTimeout(() => {
                                    setShowSuggestions(prev => ({
                                      ...prev,
                                      approvers: { ...prev.approvers, [`${requestType}_${platform}`]: false }
                                    }));
                                  }, 200);
                                }}
                              />
                              <button className="setting-user-add-btn" onClick={() => handleAddApprover(requestType, platform)}>
                                +
                              </button>
                            </div>

                            {/* Suggestions dropdown */}
                            {showSuggestions?.approvers?.[`${requestType}_${platform}`] && filteredApprovers[`${requestType}_${platform}`]?.length > 0 && (
                              <div className="user-suggestions">
                                {filteredApprovers[`${requestType}_${platform}`].map(user => (
                                  <div
                                    key={user.id}
                                    className="user-suggestion-item"
                                    onClick={() => handleSelectApprover(requestType, platform, user)}
                                  >
                                    {user.display_name}
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Only show reviewers for access-report request type */}
                        {requestType === 'access-report' && (
                          <div className="config-item">
                            <label>{t.approvers.reviewers}</label>
                            <div className="setting-user-list">
                              {(config.reviewers || []).map(user => (
                                <div key={user} className="setting-user-item">
                                  <span>{user}</span>
                                  <button className="btn-remove-option" onClick={() => handleRemoveReviewer(requestType, platform, user)} title="Remove reviewer">×</button>
                                </div>
                              ))}
                            </div>
                            <div style={{ display: 'flex', flexDirection: 'column', marginTop: 6, position: 'relative' }}>
                              <div style={{ display: 'flex' }}>
                                <input
                                  type="text"
                                  className="setting-user-input"
                                  placeholder={t.approvers.addReviewer || "Add reviewer"}
                                  value={newReviewer[`${requestType}_${platform}`] || ''}
                                  onChange={e => handleNewReviewerInput(requestType, platform, e.target.value)}
                                  onKeyDown={e => {
                                    if (e.key === 'Enter') {
                                      handleAddReviewer(requestType, platform);
                                    } else if (e.key === 'Escape') {
                                      setShowSuggestions(prev => ({
                                        ...prev,
                                        reviewers: { ...prev.reviewers, [`${requestType}_${platform}`]: false }
                                      }));
                                    }
                                  }}
                                  onBlur={() => {
                                    // Delay hiding suggestions to allow for clicks
                                    setTimeout(() => {
                                      setShowSuggestions(prev => ({
                                        ...prev,
                                        reviewers: { ...prev.reviewers, [`${requestType}_${platform}`]: false }
                                      }));
                                    }, 200);
                                  }}
                                />
                                <button className="setting-user-add-btn" onClick={() => handleAddReviewer(requestType, platform)}>
                                  +
                                </button>
                              </div>

                              {/* Suggestions dropdown */}
                              {showSuggestions?.reviewers?.[`${requestType}_${platform}`] && filteredReviewers[`${requestType}_${platform}`]?.length > 0 && (
                                <div className="user-suggestions">
                                  {filteredReviewers[`${requestType}_${platform}`].map(user => (
                                    <div
                                      key={user.id}
                                      className="user-suggestion-item"
                                      onClick={() => handleSelectReviewer(requestType, platform, user)}
                                    >
                                      {user.display_name}
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          </div>
                        )}

                        <div className="config-item">
                          <label>{t.approvers.executors}</label>
                          <div className="setting-user-list">
                            {(config.executors || []).map(user => (
                              <div key={user} className="setting-user-item">
                                <span>{user}</span>
                                <button className="btn-remove-option" onClick={() => handleRemoveExecutor(requestType, platform, user)} title="Remove executor">×</button>
                              </div>
                            ))}
                          </div>
                          <div style={{ display: 'flex', flexDirection: 'column', marginTop: 6, position: 'relative' }}>
                            <div style={{ display: 'flex' }}>
                              <input
                                type="text"
                                className="setting-user-input"
                                placeholder={t.approvers.addExecutor || "Add executor"}
                                value={newExecutor[`${requestType}_${platform}`] || ''}
                                onChange={e => handleNewExecutorInput(requestType, platform, e.target.value)}
                                onKeyDown={e => {
                                  if (e.key === 'Enter') {
                                    handleAddExecutor(requestType, platform);
                                  } else if (e.key === 'Escape') {
                                    setShowSuggestions(prev => ({
                                      ...prev,
                                      executors: { ...prev.executors, [`${requestType}_${platform}`]: false }
                                    }));
                                  }
                                }}
                                onBlur={() => {
                                  // Delay hiding suggestions to allow for clicks
                                  setTimeout(() => {
                                    setShowSuggestions(prev => ({
                                      ...prev,
                                      executors: { ...prev.executors, [`${requestType}_${platform}`]: false }
                                    }));
                                  }, 200);
                                }}
                              />
                              <button className="setting-user-add-btn" onClick={() => handleAddExecutor(requestType, platform)}>
                                +
                              </button>
                            </div>

                            {/* Suggestions dropdown */}
                            {showSuggestions?.executors?.[`${requestType}_${platform}`] && filteredExecutors[`${requestType}_${platform}`]?.length > 0 && (
                              <div className="user-suggestions">
                                {filteredExecutors[`${requestType}_${platform}`].map(user => (
                                  <div
                                    key={user.id}
                                    className="user-suggestion-item"
                                    onClick={() => handleSelectExecutor(requestType, platform, user)}
                                  >
                                    {user.display_name}
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>
            </div>
          )}

          {/* Filters Settings Tab */}
          {activeTab === 'filters' && (
            <div className="setting-tab-panel">
              <div className="filters-container">
                {Object.entries(settings.filters || {}).map(([platform, filters]) => (
                  <div key={platform} className="platform-filters">
                    <h4>{platform}</h4>
                    {Object.entries(filters || {}).map(([filterType, options]) => (
                      <div key={filterType} className="filter-group">
                        <h5>{t.filters[filterType]}</h5>
                        <div className="filter-options">
                          {(options || []).map(option => (
                            <div key={option} className="filter-option">
                              <span>{option}</span>
                              <button
                                className="btn-remove-option"
                                onClick={() => removeFilterOption(platform, filterType, option)}
                              >
                                ×
                              </button>
                            </div>
                          ))}
                        </div>
                        <div className="add-option">
                          <input
                            type="text"
                            placeholder={`${t.filters.addNew} ${t.filters[filterType]}`}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                addFilterOption(platform, filterType, e.target.value);
                                e.target.value = '';
                              }
                            }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Settings;